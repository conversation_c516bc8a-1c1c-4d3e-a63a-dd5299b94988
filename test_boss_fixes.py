#!/usr/bin/env python3
"""
测试BOSS战修复效果
验证角色选择输出信息移除和BOSS伤害修复
"""

import os
import sys
import pygame
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入项目模块
from enemy import Boss
from partner import Partner
from constants import BOSS_HUNTING_CLAW_DAMAGE, BOSS_WHIRLWIND_DAMAGE

def test_boss_damage():
    """测试BOSS伤害计算"""
    print("=== 测试BOSS伤害计算 ===")
    
    # 创建BOSS
    boss = Boss(boss_type=1)
    
    # 创建测试角色
    test_partner = Partner("test", "duck_friend_animation", 100, 50, 25)
    test_partner.hp = 100
    test_partner.is_alive = True
    test_partner.index = "测试角色"
    
    targets = [test_partner]
    
    print(f"测试前角色HP: {test_partner.hp}")
    
    # 测试普通技能
    print("\n--- 测试普通技能 ---")
    skill_result, affected_targets = boss.use_normal_skill(targets)
    print(f"技能结果: {skill_result}")
    print(f"测试后角色HP: {test_partner.hp}")
    
    # 检查伤害是否正确
    expected_damage = BOSS_HUNTING_CLAW_DAMAGE if "猎杀之爪" in skill_result else BOSS_WHIRLWIND_DAMAGE
    actual_damage = 100 - test_partner.hp
    
    print(f"预期伤害: {expected_damage}")
    print(f"实际伤害: {actual_damage}")
    
    if actual_damage == expected_damage:
        print("✅ 伤害计算正确")
        return True
    else:
        print("❌ 伤害计算错误")
        return False

def test_boss_big_move():
    """测试BOSS大招伤害"""
    print("\n=== 测试BOSS大招伤害 ===")
    
    # 创建BOSS
    boss = Boss(boss_type=1)
    
    # 创建测试角色
    test_partner = Partner("test", "duck_friend_animation", 100, 50, 25)
    test_partner.hp = 100
    test_partner.is_alive = True
    test_partner.index = "测试角色"
    
    targets = [test_partner]
    
    print(f"测试前角色HP: {test_partner.hp}")
    print(f"测试前BOSS HP: {boss.hp}")
    
    # 测试大招
    print("\n--- 测试大招 ---")
    skill_result, affected_targets = boss.use_big_move(targets)
    print(f"技能结果: {skill_result}")
    print(f"测试后角色HP: {test_partner.hp}")
    print(f"测试后BOSS HP: {boss.hp}")
    
    # 检查伤害和治疗
    from constants import BOSS_CLAIM_KING_DAMAGE, BOSS_CLAIM_KING_HEALING
    expected_damage = BOSS_CLAIM_KING_DAMAGE
    actual_damage = 100 - test_partner.hp
    
    print(f"预期伤害: {expected_damage}")
    print(f"实际伤害: {actual_damage}")
    
    if actual_damage == expected_damage:
        print("✅ 大招伤害计算正确")
        return True
    else:
        print("❌ 大招伤害计算错误")
        return False

def test_damage_consistency():
    """测试伤害一致性（多次测试）"""
    print("\n=== 测试伤害一致性 ===")
    
    damages = []
    
    for i in range(5):
        # 创建BOSS
        boss = Boss(boss_type=1)
        
        # 创建测试角色
        test_partner = Partner("test", "duck_friend_animation", 100, 50, 25)
        test_partner.hp = 100
        test_partner.is_alive = True
        test_partner.index = f"角色{i+1}"
        
        targets = [test_partner]
        
        # 强制使用猎杀之爪（修改随机数）
        import random
        random.seed(42)  # 固定种子确保一致性
        
        skill_result, affected_targets = boss.use_normal_skill(targets)
        actual_damage = 100 - test_partner.hp
        damages.append(actual_damage)
        
        print(f"第{i+1}次测试: {skill_result}, 伤害: {actual_damage}")
    
    # 检查伤害是否一致
    if len(set(damages)) == 1:
        print(f"✅ 伤害一致性测试通过，所有测试伤害都是: {damages[0]}")
        return True
    else:
        print(f"❌ 伤害不一致: {damages}")
        return False

def test_info_output():
    """测试信息输出（模拟）"""
    print("\n=== 测试信息输出 ===")
    
    # 这个测试主要是检查代码修改是否正确
    # 实际的输出测试需要在游戏运行时进行
    
    print("检查battle.py中的修改:")
    
    # 读取battle.py文件检查修改
    try:
        with open("battle.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否移除了输出信息
        if '已取消选择角色' not in content or '选中角色' not in content:
            print("✅ 角色选择输出信息已移除")
            removed_output = True
        else:
            print("❌ 角色选择输出信息仍然存在")
            removed_output = False
        
        # 检查是否移除了重复伤害应用
        if "移除重复的伤害应用" in content:
            print("✅ 重复伤害应用已移除")
            fixed_damage = True
        else:
            print("❌ 重复伤害应用修复未找到")
            fixed_damage = False
        
        return removed_output and fixed_damage
        
    except Exception as e:
        print(f"❌ 无法读取battle.py文件: {e}")
        return False

def main():
    """主函数"""
    print("=== BOSS战修复测试程序 ===")
    print()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 初始化pygame（某些模块可能需要）
    pygame.init()
    
    try:
        # 测试1: BOSS伤害计算
        damage_test = test_boss_damage()
        
        # 测试2: BOSS大招
        big_move_test = test_boss_big_move()
        
        # 测试3: 伤害一致性
        consistency_test = test_damage_consistency()
        
        # 测试4: 信息输出
        output_test = test_info_output()
        
        # 总结
        print("\n=== 测试总结 ===")
        print(f"BOSS伤害计算: {'✅ 通过' if damage_test else '❌ 失败'}")
        print(f"BOSS大招测试: {'✅ 通过' if big_move_test else '❌ 失败'}")
        print(f"伤害一致性: {'✅ 通过' if consistency_test else '❌ 失败'}")
        print(f"信息输出修复: {'✅ 通过' if output_test else '❌ 失败'}")
        
        if all([damage_test, big_move_test, consistency_test, output_test]):
            print("\n🎉 所有测试通过！BOSS战修复成功。")
            print("修复内容:")
            print("  1. ✅ 移除了角色选择时的输出信息")
            print("  2. ✅ 修复了BOSS伤害重复应用的问题")
            print("  3. ✅ 确保显示伤害与实际伤害一致")
        else:
            print("\n⚠️ 部分测试失败，请检查修复。")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    pygame.quit()

if __name__ == "__main__":
    main()
