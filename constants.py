# constants.py (修正版)
# 屏幕尺寸
SCREEN_WIDTH = 800
SCREEN_HEIGHT = 600

# 颜色
BLUE = (0, 0, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
YELLOW = (255, 255, 0)
PURPLE = (128, 0, 128)
CYAN = (0, 255, 255)
ORANGE = (255, 165, 0)
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
PINK = (255, 192, 203)   
LIGHT_GREEN = (144, 238, 144)

# 游戏参数
MAX_PARTNERS = 999  # 实际上允许无限伙伴
HP_LOSS_PER_BATTLE = 0.1
BOSS_LEVEL = 5

# 角色技能相关常量
# Normal Character Skills
NORMAL_HEAL_TARGET_HEALING = 60
NORMAL_HEAL_TARGET_MP_COST = 5
NORMAL_SHIELD_ABSORPTION = 30
NORMAL_SHIELD_TAUNT_CHANCE = 60
NORMAL_SHIELD_DAMAGE_REDUCTION = 70
NORMAL_SHIELD_MP_COST = 8
NORMAL_SHIELD_DURATION = 3
NORMAL_SHIELD_TAUNT_DURATION = 2
NORMAL_SLASH_DAMAGE = 30
NORMAL_SLASH_MP_COST = 8

# NPC Character Skills  
NPC_HEAL_ALL_HEALING = 40
NPC_HEAL_ALL_MP_COST = 6
NPC_BLESS_DAMAGE_MULTIPLIER = 1.8
NPC_BLESS_MP_COST = 10
NPC_REFLECT_TAUNT_CHANCE = 60
NPC_REFLECT_HP_RESTORE_PERCENT = 80
NPC_REFLECT_MP_COST = 12
NPC_REFLECT_TAUNT_DURATION = 2

# Boss Skills
BOSS_HP = 250
BOSS_HUNTING_CLAW_DAMAGE = 40
BOSS_HUNTING_CLAW_DAMAGE_REDUCTION = 30
BOSS_WHIRLWIND_DAMAGE = 30
BOSS_WHIRLWIND_DAMAGE_INCREASE = 20
BOSS_CLAIM_KING_DAMAGE = 50
BOSS_CLAIM_KING_HEALING = 30
BOSS_TAME_DISABLE_CHANCE = 50
BOSS_SERVE_ME_DAMAGE_MULTIPLIER = 1.5
BOSS_FATAL_TEMPTATION_DAMAGE = 15
BOSS_FATAL_TEMPTATION_DURATION = 3
BOSS_BIG_MOVE_TURN = 3

# MP恢复
NORMAL_MP_RECOVERY = 3
NPC_MP_RECOVERY = 5

# Normal/NPC 基础数值
NORMAL_BASE_HP = 100
NORMAL_BASE_MP = 60
NORMAL_ATTACK_DAMAGE = 20
NPC_BASE_HP = 120
NPC_BASE_MP = 80
NPC_ATTACK_DAMAGE = 30