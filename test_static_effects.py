#!/usr/bin/env python3
"""
测试静态特效显示
验证特效是否不再有旋转轨迹，只播放GIF动画
"""

import os
import sys
import pygame
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入项目模块
from constants import SCREEN_WIDTH, SCREEN_HEIGHT, WHITE, BLACK
from battle import VisualEffect, BattleEffect

def test_visual_effects():
    """测试VisualEffect类的静态显示"""
    print("=== 测试VisualEffect类 ===")
    
    pygame.init()
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("静态特效测试 - VisualEffect")
    
    # 创建不同类型的特效
    effects = [
        VisualEffect("heal", (200, 200)),
        VisualEffect("shield", (400, 200)),
        VisualEffect("reflect", (600, 200)),
        VisualEffect("substitute", (200, 400)),
        VisualEffect("claw", (400, 400))
    ]
    
    clock = pygame.time.Clock()
    running = True
    
    print("显示VisualEffect特效...")
    print("按ESC键退出，按SPACE键重新创建特效")
    
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_SPACE:
                    # 重新创建特效
                    effects = [
                        VisualEffect("heal", (200, 200)),
                        VisualEffect("shield", (400, 200)),
                        VisualEffect("reflect", (600, 200)),
                        VisualEffect("substitute", (200, 400)),
                        VisualEffect("claw", (400, 400))
                    ]
                    print("重新创建特效")
        
        # 清屏
        screen.fill(WHITE)
        
        # 更新和绘制特效
        for effect in effects[:]:
            effect.update()
            if effect.active:
                effect.draw(screen)
            else:
                effects.remove(effect)
        
        # 显示说明文字
        font = pygame.font.SysFont("SimHei", 24)
        texts = [
            "VisualEffect测试 - 特效应该只播放GIF动画，不旋转",
            "治疗(左上) 护盾(中上) 反射(右上)",
            "替身(左下) 爪击(右下)",
            "按SPACE重新创建特效，按ESC退出"
        ]
        
        for i, text in enumerate(texts):
            text_surface = font.render(text, True, BLACK)
            screen.blit(text_surface, (10, 10 + i * 30))
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    return True

def test_battle_effects():
    """测试BattleEffect类的静态显示"""
    print("\n=== 测试BattleEffect类 ===")
    
    pygame.init()
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("静态特效测试 - BattleEffect")
    
    # 创建模拟目标
    class MockTarget:
        def __init__(self, x, y):
            self.rect = pygame.Rect(x, y, 100, 100)
    
    targets = [
        MockTarget(150, 150),
        MockTarget(350, 150),
        MockTarget(550, 150),
        MockTarget(250, 350),
        MockTarget(450, 350)
    ]
    
    # 创建不同类型的特效
    effects = [
        BattleEffect("heal", targets[0], targets[0].rect),
        BattleEffect("claw", targets[1], targets[1].rect),
        BattleEffect("tornado", targets[2], targets[2].rect),
        BattleEffect("shake", targets[3], targets[3].rect),
        BattleEffect("heal", targets[4], targets[4].rect)
    ]
    
    clock = pygame.time.Clock()
    running = True
    
    print("显示BattleEffect特效...")
    print("按ESC键退出，按SPACE键重新创建特效")
    
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_SPACE:
                    # 重新创建特效
                    effects = [
                        BattleEffect("heal", targets[0], targets[0].rect),
                        BattleEffect("claw", targets[1], targets[1].rect),
                        BattleEffect("tornado", targets[2], targets[2].rect),
                        BattleEffect("shake", targets[3], targets[3].rect),
                        BattleEffect("heal", targets[4], targets[4].rect)
                    ]
                    print("重新创建特效")
        
        # 清屏
        screen.fill(WHITE)
        
        # 绘制目标区域
        for target in targets:
            pygame.draw.rect(screen, (200, 200, 200), target.rect, 2)
        
        # 更新和绘制特效
        for effect in effects[:]:
            effect.update()
            if effect.active:
                effect.draw(screen)
            else:
                effects.remove(effect)
        
        # 显示说明文字
        font = pygame.font.SysFont("SimHei", 24)
        texts = [
            "BattleEffect测试 - 治疗特效应该不旋转",
            "治疗(左上,右下) 爪击(中上) 龙卷风(右上) 震动(左下)",
            "按SPACE重新创建特效，按ESC退出"
        ]
        
        for i, text in enumerate(texts):
            text_surface = font.render(text, True, BLACK)
            screen.blit(text_surface, (10, 10 + i * 30))
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    return True

def main():
    """主函数"""
    print("=== 静态特效测试程序 ===")
    print("测试特效是否不再有旋转轨迹")
    print()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    try:
        # 测试1: VisualEffect类
        print("1. 测试VisualEffect类...")
        visual_success = test_visual_effects()
        
        # 测试2: BattleEffect类
        print("2. 测试BattleEffect类...")
        battle_success = test_battle_effects()
        
        # 总结
        print("\n=== 测试总结 ===")
        print(f"VisualEffect测试: {'✅ 通过' if visual_success else '❌ 失败'}")
        print(f"BattleEffect测试: {'✅ 通过' if battle_success else '❌ 失败'}")
        
        if visual_success and battle_success:
            print("\n🎉 所有测试通过！特效现在应该只播放GIF动画，不再有旋转轨迹。")
        else:
            print("\n⚠️ 部分测试失败。")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
