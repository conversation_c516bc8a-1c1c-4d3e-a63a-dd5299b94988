#!/usr/bin/env python3
"""
测试BOSS战准备界面的角色选择布局优化
"""

import pygame
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入必要的模块
from constants import SCREEN_WIDTH, SCREEN_HEIGHT, WHITE, BLACK
from duck import Duck
from game_state import BattlePrepState
from partner import Partner

def create_test_partners():
    """创建测试用的伙伴"""
    partners = []
    npc_types = ["duck_friend_animation", "good_boy_animation", "bat_animation", "lady_duck_animation", "mushroom_animation"]

    for i, npc_type in enumerate(npc_types):
        partner = Partner(100 + i * 50, 100, i, is_special=True, npc_type=npc_type)
        partner.attack_power = 50 + i * 10
        # 模拟技能分配
        partner.assigned_skill = f"skill_{i}"
        partner.get_skill_info = lambda: {"name": f"技能{i}", "icon": "default_skill.png"}
        partners.append(partner)

    return partners

def main():
    """主测试函数"""
    pygame.init()

    # 创建屏幕
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("BOSS战准备界面测试")

    # 创建字体
    font = pygame.font.SysFont("SimHei", 32)

    # 创建测试用的鸭子和伙伴
    duck = Duck(100, 100)  # 提供x, y坐标
    duck.partners = create_test_partners()
    duck.weapons = []  # 简化测试，不添加武器
    duck.buffs = []

    # 创建BOSS战准备状态
    battle_prep = BattlePrepState(duck, font)

    # 游戏主循环
    clock = pygame.time.Clock()
    running = True

    print("BOSS战准备界面测试启动")
    print("使用A/D键滑动角色，鼠标点击选择角色")
    print("按ESC退出测试")

    while running:
        # 处理事件
        events = pygame.event.get()
        keys = pygame.key.get_pressed()

        for event in events:
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False

        # 更新界面
        screen.fill(WHITE)

        # 更新BOSS战准备状态
        if not battle_prep.update(screen, keys, events):
            print("进入战斗！")
            running = False

        # 显示测试信息
        info_text = font.render("测试：BOSS战准备界面布局优化", True, BLACK)
        screen.blit(info_text, (10, 10))

        pygame.display.flip()
        clock.tick(60)

    pygame.quit()
    print("测试结束")

if __name__ == "__main__":
    main()
