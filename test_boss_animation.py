#!/usr/bin/env python3
"""
测试BOSS攻击动画
验证boss_attacking_animation.gif是否被正确调用和显示
"""

import os
import sys
import pygame
import logging

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入项目模块
from constants import SCREEN_WIDTH, SCREEN_HEIGHT, WHITE, BLACK
from enemy import Boss
from partner import Partner

def create_test_partners():
    """创建测试用的伙伴"""
    partners = []
    for i in range(3):
        partner = Partner(100 + i * 50, 100, i, is_special=True, npc_type="duck_friend_animation")
        partner.attack_power = 50 + i * 10
        partner.hp = 100
        partner.max_hp = 100
        partner.is_alive = True
        partners.append(partner)
    return partners

def main():
    """主测试函数"""
    pygame.init()
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 创建屏幕
    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
    pygame.display.set_caption("BOSS攻击动画测试")
    
    # 创建字体
    font = pygame.font.SysFont("SimHei", 24)
    
    # 创建BOSS
    boss = Boss(boss_type=1)
    
    # 创建测试伙伴
    test_partners = create_test_partners()
    
    # 游戏主循环
    clock = pygame.time.Clock()
    running = True
    
    # 测试状态
    animation_triggered = False
    last_skill_time = 0
    skill_cooldown = 3000  # 3秒冷却
    
    print("BOSS攻击动画测试启动")
    print("按SPACE键触发BOSS攻击动画")
    print("按ESC键退出测试")
    print(f"BOSS攻击动画帧数: {len(boss.attacking_frames)}")
    
    while running:
        current_time = pygame.time.get_ticks()
        
        # 处理事件
        events = pygame.event.get()
        keys = pygame.key.get_pressed()
        
        for event in events:
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    running = False
                elif event.key == pygame.K_SPACE:
                    # 触发BOSS攻击
                    if current_time - last_skill_time > skill_cooldown:
                        print("触发BOSS攻击动画...")
                        skill_result, affected_targets = boss.use_skill(test_partners)
                        print(f"技能结果: {skill_result}")
                        print(f"受影响目标: {[t.index for t in affected_targets]}")
                        print(f"BOSS攻击动画状态: {boss.is_attacking}")
                        animation_triggered = True
                        last_skill_time = current_time
        
        # 更新界面
        screen.fill(WHITE)
        
        # 绘制BOSS
        boss.draw(screen)
        
        # 绘制测试伙伴
        for i, partner in enumerate(test_partners):
            if partner.is_alive:
                x = 100 + i * 150
                y = 400
                
                # 绘制伙伴（简单矩形）
                partner_rect = pygame.Rect(x, y, 60, 60)
                pygame.draw.rect(screen, (0, 255, 0), partner_rect)
                
                # 显示伙伴信息
                info_text = font.render(f"伙伴{partner.index}", True, BLACK)
                screen.blit(info_text, (x, y + 70))
                
                hp_text = font.render(f"HP: {partner.hp}/{partner.max_hp}", True, BLACK)
                screen.blit(hp_text, (x, y + 95))
        
        # 显示BOSS信息
        boss_info = [
            f"BOSS HP: {boss.hp}/{boss.max_hp}",
            f"回合计数: {boss.turn_counter}",
            f"攻击动画: {'播放中' if boss.is_attacking else '未播放'}",
            f"动画帧数: {len(boss.attacking_frames)}",
            f"当前帧: {boss.current_attack_frame if boss.is_attacking else 'N/A'}"
        ]
        
        for i, info in enumerate(boss_info):
            text = font.render(info, True, BLACK)
            screen.blit(text, (10, 10 + i * 30))
        
        # 显示控制说明
        controls = [
            "按SPACE键触发BOSS攻击",
            "按ESC键退出测试",
            f"冷却时间: {max(0, skill_cooldown - (current_time - last_skill_time)) // 1000}秒"
        ]
        
        for i, control in enumerate(controls):
            text = font.render(control, True, BLACK)
            screen.blit(text, (10, SCREEN_HEIGHT - 100 + i * 25))
        
        # 显示动画状态
        if boss.is_attacking:
            status_text = font.render("🎬 BOSS攻击动画播放中...", True, (255, 0, 0))
            screen.blit(status_text, (SCREEN_WIDTH // 2 - 150, 50))
            
            # 显示动画进度
            if boss.attack_animation_duration > 0:
                elapsed = current_time - boss.attack_animation_start_time
                progress = min(1.0, elapsed / boss.attack_animation_duration)
                progress_text = font.render(f"动画进度: {progress:.1%}", True, (255, 0, 0))
                screen.blit(progress_text, (SCREEN_WIDTH // 2 - 100, 80))
        elif animation_triggered:
            status_text = font.render("✅ 动画播放完成", True, (0, 150, 0))
            screen.blit(status_text, (SCREEN_WIDTH // 2 - 100, 50))
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()
    print("测试结束")

if __name__ == "__main__":
    main()
