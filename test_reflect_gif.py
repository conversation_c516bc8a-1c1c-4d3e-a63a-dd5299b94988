#!/usr/bin/env python3
"""
测试生成的反射效果GIF文件
验证GIF文件是否可以正常加载和显示
"""

import os
import sys
import pygame
from PIL import Image

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入项目模块
from config import ASSETS_DIR
from animation_utils import load_animation_frames

def test_gif_with_pil():
    """使用PIL测试GIF文件"""
    print("=== 使用PIL测试GIF文件 ===")

    gif_path = os.path.join(ASSETS_DIR, "reflect_effect.gif")

    if not os.path.exists(gif_path):
        print(f"❌ GIF文件不存在: {gif_path}")
        return False

    try:
        gif = Image.open(gif_path)

        print(f"✅ 成功打开GIF文件")
        print(f"  文件路径: {gif_path}")
        print(f"  图像尺寸: {gif.size}")
        print(f"  图像模式: {gif.mode}")
        print(f"  帧数: {gif.n_frames}")

        # 检查每一帧
        for i in range(gif.n_frames):
            gif.seek(i)
            print(f"  第{i+1}帧: 尺寸{gif.size}, 模式{gif.mode}")

        return True

    except Exception as e:
        print(f"❌ PIL测试失败: {e}")
        return False

def test_gif_with_animation_utils():
    """使用animation_utils测试GIF文件"""
    print("\n=== 使用animation_utils测试GIF文件 ===")

    # 初始化pygame显示（animation_utils需要）
    pygame.init()
    pygame.display.set_mode((1, 1))  # 创建最小显示窗口

    try:
        frames, is_animated = load_animation_frames("reflect_effect.gif", target_size=(96, 96))

        if frames:
            print(f"✅ 成功加载动画帧")
            print(f"  帧数: {len(frames)}")
            print(f"  是否为动画: {is_animated}")
            print(f"  第一帧尺寸: {frames[0].get_size()}")
            print(f"  第一帧格式: {frames[0].get_flags()}")

            # 检查透明度支持
            if frames[0].get_flags() & pygame.SRCALPHA:
                print(f"  ✅ 支持透明度")
            else:
                print(f"  ⚠️ 不支持透明度")

            return True
        else:
            print(f"❌ 无法加载动画帧")
            return False

    except Exception as e:
        print(f"❌ animation_utils测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gif_display():
    """测试GIF显示效果"""
    print("\n=== 测试GIF显示效果 ===")

    pygame.init()

    try:
        # 创建测试窗口
        screen = pygame.display.set_mode((400, 400))
        pygame.display.set_caption("反射效果GIF测试")

        # 加载动画帧
        frames, is_animated = load_animation_frames("reflect_effect.gif", target_size=(96, 96))

        if not frames:
            print("❌ 无法加载动画帧")
            return False

        print(f"✅ 开始显示测试")
        print(f"  按ESC键退出测试")
        print(f"  动画将在窗口中央播放")

        clock = pygame.time.Clock()
        current_frame = 0
        frame_timer = 0
        frame_duration = 200  # 200毫秒每帧

        running = True
        while running:
            # 处理事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False

            # 更新动画帧
            frame_timer += clock.get_time()
            if frame_timer >= frame_duration:
                current_frame = (current_frame + 1) % len(frames)
                frame_timer = 0

            # 绘制
            screen.fill((50, 50, 50))  # 深灰色背景

            # 在中央绘制动画
            x = (400 - 96) // 2
            y = (400 - 96) // 2
            screen.blit(frames[current_frame], (x, y))

            # 显示帧信息
            font = pygame.font.Font(None, 36)
            text = font.render(f"Frame {current_frame + 1}/{len(frames)}", True, (255, 255, 255))
            screen.blit(text, (10, 10))

            pygame.display.flip()
            clock.tick(60)

        print(f"✅ 显示测试完成")
        return True

    except Exception as e:
        print(f"❌ 显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        pygame.quit()

def main():
    """主函数"""
    print("=== 反射效果GIF测试程序 ===")

    # 测试1: PIL测试
    pil_success = test_gif_with_pil()

    # 测试2: animation_utils测试
    utils_success = test_gif_with_animation_utils()

    # 测试3: 显示测试
    display_success = test_gif_display()

    # 总结
    print("\n=== 测试总结 ===")
    print(f"PIL测试: {'✅ 通过' if pil_success else '❌ 失败'}")
    print(f"animation_utils测试: {'✅ 通过' if utils_success else '❌ 失败'}")
    print(f"显示测试: {'✅ 通过' if display_success else '❌ 失败'}")

    if all([pil_success, utils_success, display_success]):
        print("\n🎉 所有测试通过！reflect_effect.gif 文件可以正常使用。")
    else:
        print("\n⚠️ 部分测试失败，请检查GIF文件。")

if __name__ == "__main__":
    main()
